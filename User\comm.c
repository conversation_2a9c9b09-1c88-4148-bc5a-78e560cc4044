/*
 * comm.c - 太阳能逆变器通信模块
 *
 * 功能描述:
 * 本模块负责太阳能逆变器的通信功能
 * 包括数据发送、消息队列管理、协议处理等
 *
 * 主要功能:
 * 1. 消息队列管理和发送
 * 2. 系统数据上报
 * 3. 故障信息通信
 * 4. 调试信息输出
 *
 * 创建时间: 2024年12月23日
 * 作者: ZH
 */

#include "comm.h"
#include "crc16.h"
#include "solar.h"

// ========== 宏定义 ==========
#define MSG_MAX_LEN (sizeof(h.message_list) / sizeof(h.message_list[0])) // 消息队列最大长度

// ========== 函数声明 ==========
void get_meter_data(void); // 获取电表数据函数声明

/**
 * @brief 太阳能逆变器消息发送函数
 * @param msg 要发送的消息字符串指针
 *
 * 功能: 将消息添加到发送队列中
 * 调用时机: 需要发送调试信息或故障信息时调用
 *
 * 队列管理:
 * 使用环形缓冲区管理消息队列
 * 防止队列溢出，确保系统稳定性
 *
 * 应用场景:
 * - 故障信息上报
 * - 调试信息输出
 * - 状态变化通知
 */
void solar_send_msg(const char *msg)
{
    // 检查消息队列是否有空间
    if (h.message_count < MSG_MAX_LEN)
    {
        // 将消息添加到队列尾部 (环形缓冲区)
        h.message_list[(h.message_head + h.message_count) % MSG_MAX_LEN] = msg;
        h.message_count++; // 增加消息计数
    }
    // 注意: 队列满时丢弃新消息，避免系统阻塞
}

/**
 * @brief 太阳能逆变器通信器主函数
 *
 * 功能: 管理系统的通信任务
 * 调用时机: 在主循环中周期性调用
 *
 * 主要任务:
 * 1. 处理消息队列发送
 * 2. 发送故障信息
 * 3. 定时上报系统数据
 *
 * 时序控制:
 * 使用时间戳控制发送频率，避免通信阻塞
 * 确保重要信息能够及时发送
 */
void solar_Communicator(void)
{
    // 静态变量用于时序控制
    static uint32_t send_solar_data_tick = 0, send_solar_data_complete_tick = 0;

    // ========== 消息队列处理 ==========
    if (h.message_count > 0) // 有待发送消息
    {
        // 检查发送间隔 (200ms)
        if (SOLAR_GET_TICK_COUNT(send_solar_data_complete_tick) > 200)
        {
            // 发送队列中的所有消息
            while (h.message_count)
            {
                Solar_Scia_Printf("\n%s\n", h.message_list[h.message_head]); // 发送消息
                h.message_head = (h.message_head + 1) % MSG_MAX_LEN;          // 移动队列头指针
                h.message_count--;                                           // 减少消息计数
            }
            Solar_Scia_Printf("\0"); // 发送结束标志
        }
        send_solar_data_tick = SOLAR_GET_TICK(); // 更新发送时间戳
    }

    if (h.hProtect.protect_flag)
    {
        if (SOLAR_GET_TICK_COUNT(send_solar_data_complete_tick) > 200)
        {
            Solar_Scia_Printf("\nEC[%ld](%s)\n\n", h.hProtect.protect_code, h.hProtect.text_label);

            h.hProtect.protect_flag = 0;
        }
        send_solar_data_tick = SOLAR_GET_TICK(); // ???
    }

    //    {
    //        static uint32_t tick = 0;
    //        if (SOLAR_GET_TICK_COUNT(tick) > 2500)
    //        {
    //            tick = SOLAR_GET_TICK();
    //            Solar_Scia_Printf("cntl_power:%d\n", (int)h.hPvboost.power_ctlr);
    //            Solar_Scia_Printf("theta_comp:%d\n", (int)(h.spll_theta_compensation*1000));
    //
    //            send_solar_data_tick = SOLAR_GET_TICK(); //???????
    //        }
    //    }

    {
        if (SOLAR_GET_TICK_COUNT(send_solar_data_tick) > 1000)
        {
            send_solar_data_tick = SOLAR_GET_TICK();

            get_meter_data();

            static int tx_flag = 0;

            if (tx_flag)
            {
                tx_flag = 0;

                Solar_Scia_Printf("Iabc:%d,%d,%d;LC:%d\n", (int )(h.meter_Ia * 100), (int )(h.meter_Ib * 100), (int )(h.meter_Ic * 100), (int )(h.meter_LC * 10000));
            }
            else
            {
                tx_flag = 1;

                h.data.Vpv1 = h.Vpv1.out_instant;
                h.data.Ipv1 = h.Ipv1.out_instant;
                h.data.Vpv2 = h.Vpv2.out_instant;
                h.data.Ipv2 = h.Ipv2.out_instant;
                h.data.VBus = h.Vbus;
                h.data.VBusPos = h.Vbus_pos.out_instant;
                h.data.VBusNeg = h.Vbus_neg.out_instant;

                h.data.GVa = h.Vgrid_a.out_rms_xp;
                h.data.GVb = h.Vgrid_b.out_rms_xp;
                h.data.GVc = h.Vgrid_c.out_rms_xp;

                h.data.Ia = h.Ia.out_rms_xp;
                h.data.Ib = h.Ib.out_rms_xp;
                h.data.Ic = h.Ic.out_rms_xp;

                h.data.Freq = h.spll_freq;

                h.data.boost1_pwm = ECap1Regs.CAP4;
                h.data.boost2_pwm = ECap2Regs.CAP4;

                h.data.Vpvref_mpptOut1 = h.Vpvref_mpptOut1;
                h.data.Vpvref_mpptOut2 = h.spll_theta_compensation * (180 / M_PI); // h.Vpvref_mpptOut2;

                h.data.Input_Power_MPPT1 = h.Input_Power_MPPT1;
                h.data.Input_Power_MPPT2 = h.Input_Power_MPPT2;
                h.data.Input_Power_Total = h.Input_Power_Total;

                h.data.Output_Power_a = h.Output_Power_a_rms_xp;
                h.data.Output_Power_b = h.Output_Power_b_rms_xp;
                h.data.Output_Power_c = h.Output_Power_c_rms_xp;
                h.data.Output_Power_T = h.Output_Power_Total_rms_xp;

                h.data.tim_program_elapsed_time = hdebug.time2_count_max;
                //            hdebug.time2_count_max = 0;

                h.data.protect_word[0] = h.hProtect.protect_word[0];
                h.data.protect_word[1] = h.hProtect.protect_word[1];

                h.data.solar_state = h.state;

                Solar_SciBlockTransmit8bit(&hScia, (uint16_t*) &(h.data), sizeof(h.data) * 2);

                //    static  uint16_t Vga[200], Vgb[200], Vgc[200];
                //     memcpy(Vga, h.Grid_Ua.adc_buf, 200);
                //     memcpy(Vgb, h.Grid_Ub.adc_buf, 200);
                //     memcpy(Vgc, h.Grid_Uc.adc_buf, 200);

                //     Solar_SciBlockTransmit8bit(&hScia, (uint16_t *)(Vga), 400);
                //     Solar_SciBlockTransmit8bit(&hScia, (uint16_t *)(Vgb), 400);
                //     Solar_SciBlockTransmit8bit(&hScia, (uint16_t *)(Vgc), 400);

                Solar_SciBlockTransmit8bit(&hScia, (uint16_t*) (h.Vgrid_a.adc_buf), 400);
                Solar_SciBlockTransmit8bit(&hScia, (uint16_t*) (h.Vgrid_b.adc_buf), 400);
                Solar_SciBlockTransmit8bit(&hScia, (uint16_t*) (h.Vgrid_c.adc_buf), 400);

                Solar_SciBlockTransmit8bit(&hScia, (uint16_t*) (h.Vinv_a.adc_buf), 400);
                Solar_SciBlockTransmit8bit(&hScia, (uint16_t*) (h.Vinv_b.adc_buf), 400);
                Solar_SciBlockTransmit8bit(&hScia, (uint16_t*) (h.Vinv_c.adc_buf), 400);

                Solar_SciBlockTransmit8bit(&hScia, (uint16_t*) (h.Ia.adc_buf), 400);
                Solar_SciBlockTransmit8bit(&hScia, (uint16_t*) (h.Ib.adc_buf), 400);
                Solar_SciBlockTransmit8bit(&hScia, (uint16_t*) (h.Ic.adc_buf), 400);

                Solar_SciBlockTransmit8bit(&hScia, (uint16_t*) (hdebug.dlog_buff), 400 * 4);
                Solar_SciBlockTransmit8bit(&hScia, (uint16_t*) (hdebug.data_buff), 400 * 8);

                send_solar_data_complete_tick = SOLAR_GET_TICK();
            }
        }
    }
}

#define RS485_MODE_REC()
#define RS485_MODE_SEND()

union rx_val
{
    float value;
    struct
    {
        uint32_t d :8;
        uint32_t c :8;
        uint32_t b :8;
        uint32_t a :8;
    } byte;
};

char tx_frame_I[] = { 0x01, 0x03, 00, 12, 00, 6, 0, 0 };
char tx_frame_LC[] = { 0x01, 0x03, 00, 48, 00, 2, 0, 0 };

enum
{
    FRAME_BUSY, FRAME_I, FRAME_LC
} tx_state = FRAME_I;

#define tx_data_size (sizeof(tx_data))

void scib_rx_idle_cb(uint8_t *Rx_Buf, uint16_t Rx_Count)
{
    if (0 != crc16_calc(Rx_Buf, Rx_Count))
    {
        return;
    }

    static int debug_rx_count = 0;
    debug_rx_count++;

    static float Iavg;

    if (Rx_Count == 17)
    {
        static union rx_val rx_Ia, rx_Ib, rx_Ic;

        rx_Ia.byte.a = Rx_Buf[3];
        rx_Ia.byte.b = Rx_Buf[4];
        rx_Ia.byte.c = Rx_Buf[5];
        rx_Ia.byte.d = Rx_Buf[6];

        rx_Ib.byte.a = Rx_Buf[7];
        rx_Ib.byte.b = Rx_Buf[8];
        rx_Ib.byte.c = Rx_Buf[9];
        rx_Ib.byte.d = Rx_Buf[10];

        rx_Ic.byte.a = Rx_Buf[11];
        rx_Ic.byte.b = Rx_Buf[12];
        rx_Ic.byte.c = Rx_Buf[13];
        rx_Ic.byte.d = Rx_Buf[14];

        h.meter_Ia = rx_Ia.value;
        h.meter_Ib = rx_Ib.value;
        h.meter_Ic = rx_Ic.value;

        Iavg = (h.meter_Ia + h.meter_Ib + h.meter_Ic) / 3;

        h.Ia_gain += (Iavg - h.meter_Ia) * 0.01;
        h.Ib_gain += (Iavg - h.meter_Ib) * 0.01;
        h.Ic_gain += (Iavg - h.meter_Ic) * 0.01;

        h.Ia_gain = h.Ia_gain > 1 ? 1 : h.Ia_gain < 0.98 ? 0.98 : h.Ia_gain;
        h.Ib_gain = h.Ib_gain > 1 ? 1 : h.Ib_gain < 0.98 ? 0.98 : h.Ib_gain;
        h.Ic_gain = h.Ic_gain > 1 ? 1 : h.Ic_gain < 0.98 ? 0.98 : h.Ic_gain;

        tx_state = FRAME_LC;

    }
    else if (Rx_Count == 9)
    {
        static union rx_val rx_LC;

        rx_LC.byte.a = Rx_Buf[3];
        rx_LC.byte.b = Rx_Buf[4];
        rx_LC.byte.c = Rx_Buf[5];
        rx_LC.byte.d = Rx_Buf[6];

        h.meter_LC = rx_LC.value;

        if (Iavg > 1.5)
        {
            if (h.meter_LC > 0)
            {
                h.LC_gain += (0.995 - h.meter_LC) * 0.1;
            }
            else
            {
                h.LC_gain += -0.001;
            }

            h.LC_gain = h.LC_gain > 0.01 ? 0.01 : h.LC_gain < -0.01 ? -0.01 : h.LC_gain;
        }
        tx_state = FRAME_I;
    }

}
void mb_rtu_send(char *frame)
{
    uint16_t crc = crc16_calc((uint16_t*) frame, 6);
    frame[6] = crc & 0xFF;
    frame[7] = (crc >> 8) & 0xFF;

    SCI_Transmit(&hScib, (uint16_t*) frame, 8);

}
void get_meter_data(void)
{
    if (tx_state == FRAME_I)
    {
        tx_state = FRAME_BUSY;
        mb_rtu_send(tx_frame_I);
    }
    else if (tx_state == FRAME_LC)
    {
        tx_state = FRAME_BUSY;
        mb_rtu_send(tx_frame_LC);
    }

}
