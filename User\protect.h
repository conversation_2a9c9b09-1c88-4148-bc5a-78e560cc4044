/*
 * protect.h - 太阳能逆变器保护系统头文件
 *
 * 功能说明：定义了逆变器系统的各种保护机制，包括电流、电压、频率等参数的保护
 * 适用于10KW光伏逆变器系统的安全保护功能实现
 *
 *  Created on: 2024年2月19日
 *      Author: adam
 */

#ifndef SOURCE_PROTECT_H_  // 防止头文件重复包含的宏定义开始
#define SOURCE_PROTECT_H_  // 定义头文件保护宏

#include "common.h"  // 包含通用定义和数据类型的头文件



/**
 * @brief 保护错误代码枚举类型
 *
 * 定义了逆变器系统中所有可能的保护错误类型
 * 使用32位分组的方式组织错误代码，便于位操作和状态管理
 */
enum protect_code_t
{
    // 第0组保护代码 (0-31) - 立即保护类型
    PROT_WORD_0 = 32 * 0,     // 保护字0的起始位置，用于位操作分组
    PROT_PowerOff,            // 系统关机保护
    PROT_Ia_Over,             // A相输出电流过流保护
    PROT_Ib_Over,             // B相输出电流过流保护
    PROT_Ic_Over,             // C相输出电流过流保护
    PROT_Ipv1_Over,           // 光伏串1直流输入电流过流保护
    PROT_Ipv2_Over,           // 光伏串2直流输入电流过流保护

    PROT_Grid_Ua_Over,        // 电网A相电压过压保护
    PROT_Grid_Ua_Under,       // 电网A相电压欠压保护
    PROT_Grid_Ub_Over,        // 电网B相电压过压保护
    PROT_Grid_Ub_Under,       // 电网B相电压欠压保护
    PROT_Grid_Uc_Over,        // 电网C相电压过压保护
    PROT_Grid_Uc_Under,       // 电网C相电压欠压保护
    PROT_Grid_Freq_Over,      // 电网频率过高保护
    PROT_Grid_Freq_Under,     // 电网频率过低保护

    // 第1组保护代码 (32-63) - 立即保护类型
    PROT_WORD_1 = 32 * 1,     // 保护字1的起始位置，用于位操作分组
    PROT_Vpv1_Over,           // 光伏串1直流电压过压保护
    PROT_Vpv1_Under,          // 光伏串1直流电压欠压保护
    PROT_Vpv2_Over,           // 光伏串2直流电压过压保护
    PROT_Vpv2_Under,          // 光伏串2直流电压欠压保护
    PROT_VBusBalance_Over,    // 直流母线电压不平衡过限保护
    PROT_VBusBalance_Under,   // 直流母线电压不平衡欠限保护
    PROT_VBusSum_Over,        // 直流母线总电压过压保护
    PROT_VBusSum_Under,       // 直流母线总电压欠压保护

    PROT_PV_Power_Over,           // 直流输入功率过载保护
    PROT_PV_underpower,           // 太阳能功率不足保护（低功率运行保护）
    PROT_unintentional_islanding, // 非计划孤岛效应保护（防止意外孤岛运行）

    // 预留的保护代码组，用于系统扩展
    PROT_WORD_2 = 32 * 2,     // 保护字2的起始位置（64-95）
    PROT_WORD_3 = 32 * 3,     // 保护字3的起始位置（96-127）
    PROT_WORD_4 = 32 * 4,     // 保护字4的起始位置（128-159）
    PROT_WORD_5 = 32 * 5,     // 保护字5的起始位置（160-191）
    PROT_WORD_6 = 32 * 6,     // 保护字6的起始位置（192-223）
    PROT_WORD_7 = 32 * 7,     // 保护字7的起始位置（224-255）

    PROT_ERROR_MAX = 0x7FFFFFFF,  // 保护错误代码的最大值，用于边界检查
};

/**
 * @brief 保护处理句柄结构体
 *
 * 用于管理系统中的保护状态和错误信息
 * 提供统一的保护错误处理接口
 */
typedef struct Protect_Handle_t
{
    enum protect_code_t protect_code;  // 当前激活的保护错误代码
    uint32_t protect_word[10];         // 保护状态字数组，每个字包含32个保护位
    uint32_t protect_flag;             // 保护标志位，用于快速判断是否有保护激活
    const char *text_label;            // 保护错误的文本描述标签，用于显示和调试
} Protect_Handle_t;

/**
 * @brief 保护成员结构体
 *
 * 定义单个保护参数的完整保护逻辑，包括阈值、延时、状态等
 * 适用于电压、电流、频率等模拟量参数的保护实现
 */
typedef struct Protect_Member_t
{
    /**
     * @brief 保护动作标志枚举
     *
     * 定义保护功能的当前状态
     */
    enum _ActFlag
    {
        PROT_DISABLE, // 保护功能禁用状态，不进行保护检测
        PROT_NORMAL,  // 正常运行状态，参数在安全范围内
        PROT_OVER,    // 过上限保护状态，参数超过上限阈值
        PROT_UNDER    // 过下限保护状态，参数低于下限阈值
    } ActFlag;        // 当前保护状态标志

    float ActValue;          // 当前实际测量值，用于保护判断和显示
    float ActUpperThreshold; // 上限保护动作阈值，超过此值触发过限保护
    float ActLowerThreshold; // 下限保护动作阈值，低于此值触发欠限保护
    uint32_t ActDelay;       // 保护动作确认延时时间（单位：控制周期）
    uint32_t ExtDelay;       // 保护退出确认延时时间（单位：控制周期）
    uint32_t ActCount;       // 保护动作延时计数器，用于防止误动作
    uint32_t ExtCount;       // 保护退出延时计数器，用于防止频繁切换
    char *OverLabel;         // 过限保护的文本标签，用于错误显示
    char *UnderLabel;        // 欠限保护的文本标签，用于错误显示

    enum protect_code_t over_prot_code;   // 过限保护对应的错误代码
    enum protect_code_t under_prot_code;  // 欠限保护对应的错误代码

} Protect_Member_t;

/**
 * @brief 初始化保护成员结构体
 *
 * @param hProt 指向保护成员结构体的指针
 * @param ActUpperThreshold 上限保护阈值
 * @param ActLowerThreshold 下限保护阈值
 * @param ActDelay 保护动作延时时间（控制周期数）
 * @param ExtDelay 保护退出延时时间（控制周期数）
 * @param Upper_error_cord 过限保护错误代码
 * @param Lower_error_cord 欠限保护错误代码
 * @param OverLabel 过限保护文本标签
 * @param UnderLabel 欠限保护文本标签
 *
 * 功能：配置保护参数的所有初始值，为保护功能做准备
 */
void Protect_Member_Init(Protect_Member_t *hProt,
                         float ActUpperThreshold, float ActLowerThreshold,
                         uint32_t ActDelay, uint32_t ExtDelay,
                         enum protect_code_t Upper_error_cord, enum protect_code_t Lower_error_cord,
                         char *OverLabel, char *UnderLabel);

/**
 * @brief 执行保护成员的保护检查
 *
 * @param hProt 指向保护成员结构体的指针
 * @param Value 当前需要检查的实际测量值
 *
 * 功能：根据当前测量值和配置的阈值进行保护逻辑判断
 *       包括保护动作、保护退出的延时确认处理
 */
void Protect_Member_Check(Protect_Member_t *hProt, float Value);

/**
 * @brief 设置保护错误代码
 *
 * @param error_code 要设置的保护错误代码
 * @param text 错误描述文本
 *
 * 功能：在系统保护状态字中设置对应的错误位，并记录错误信息
 */
void protect_set_error_code(enum protect_code_t error_code, const char *text);

/**
 * @brief 复位保护错误代码
 *
 * @param error_cord 要复位的保护错误代码
 *
 * 功能：清除系统保护状态字中对应的错误位，表示保护条件已消除
 */
void protect_reset_error_code(enum protect_code_t error_cord);

/**
 * @brief 电流保护检查宏定义
 *
 * @param hProt 保护成员结构体实例（非指针）
 * @param Val 当前电流测量值
 *
 * 功能说明：
 * 专门用于电流过流保护的快速检查宏，只检查上限过流保护
 * 适用于逆变器输出电流和光伏输入电流的保护
 *
 * 保护逻辑：
 * 1. 正常状态下：检查是否超过上限阈值，超过则开始计时
 * 2. 保护状态下：检查是否低于下限阈值，低于则开始退出计时
 * 3. 使用延时确认机制防止瞬时干扰导致的误保护
 *
 * 注意：此宏只处理过流保护，不处理欠流保护
 */
#define PROTECT_CURRENT_CHECK_MACRO(hProt, Val)                                    \
    {                                                                              \
        float Value = (Val);                        /* 获取当前电流测量值 */       \
        if (hProt.ActFlag == PROT_NORMAL)          /* 当前处于正常运行状态 */      \
        {                                                                          \
            if (Value > hProt.ActUpperThreshold)   /* 电流超过过流保护阈值 */      \
            {                                                                      \
                hProt.ActCount++;                   /* 过流保护延时计数递增 */     \
                if (hProt.ActCount >= hProt.ActDelay) /* 达到保护动作延时时间 */   \
                {                                                                  \
                    protect_set_error_code(hProt.over_prot_code, hProt.OverLabel); /* 设置过流保护错误 */ \
                    hProt.ActCount = 0;             /* 清零保护动作计数器 */        \
                    hProt.ActFlag = PROT_OVER;      /* 切换到过流保护状态 */        \
                    hProt.ActValue = Value;         /* 记录触发保护时的电流值 */    \
                }                                                                  \
            }                                                                      \
            else                                                                   \
            {                                                                      \
                hProt.ActCount = 0;                 /* 电流正常，清零计数器 */     \
            }                                                                      \
        }                                                                          \
        else                                        /* 当前处于保护状态 */         \
        {                                                                          \
            if (Value < hProt.ActLowerThreshold)    /* 电流低于退出保护阈值 */     \
            {                                                                      \
                hProt.ExtCount++;                   /* 保护退出延时计数递增 */     \
                if (hProt.ExtCount >= hProt.ExtDelay) /* 达到保护退出延时时间 */   \
                {                                                                  \
                    protect_reset_error_code(hProt.over_prot_code); /* 清除过流保护错误 */ \
                    hProt.ExtCount = 0;             /* 清零保护退出计数器 */        \
                    hProt.ActFlag = PROT_NORMAL;    /* 恢复到正常运行状态 */        \
                }                                                                  \
            }                                                                      \
            else                                                                   \
            {                                                                      \
                hProt.ActValue = Value;             /* 更新当前电流值 */           \
                hProt.ExtCount = 0;                 /* 电流仍超限，清零退出计数 */ \
            }                                                                      \
        }                                                                          \
    }

/**
 * @brief 通用保护成员检查宏定义
 *
 * @param h 保护成员结构体实例（非指针）
 * @param Val 当前测量值（电压、频率等参数）
 *
 * 功能说明：
 * 通用的保护检查宏，同时支持过限和欠限双向保护
 * 适用于电压、频率等需要上下限双向保护的参数
 *
 * 保护逻辑：
 * 1. 正常状态：检查是否超出上下限范围，超出则开始保护延时
 * 2. 禁用状态：仅更新测量值，不进行保护判断
 * 3. 保护状态：检查是否回到安全范围，是则开始退出延时
 * 4. 支持过限和欠限两种保护类型的独立处理
 *
 * 适用场景：电网电压保护、频率保护、直流电压保护等
 */
#define PROTECT_MEMBER_CHECK_MACRO(h, Val)                                       \
    {                                                                            \
        float Value = (Val);                        /* 获取当前测量值 */         \
        if (h.ActFlag == PROT_NORMAL)              /* 当前处于正常运行状态 */    \
        {                                                                        \
            if (Value > h.ActUpperThreshold || Value < h.ActLowerThreshold)      /* 参数超出安全范围 */ \
            {                                                                    \
                h.ActCount++;                       /* 保护动作延时计数递增 */   \
                if (h.ActCount >= h.ActDelay)       /* 达到保护动作延时时间 */   \
                {                                                                \
                    if (Value > h.ActUpperThreshold) /* 判断是过限还是欠限 */     \
                    {                                                            \
                        protect_set_error_code(h.over_prot_code, h.OverLabel);   /* 设置过限保护错误 */ \
                        h.ActFlag = PROT_OVER;       /* 切换到过限保护状态 */     \
                    }                                                            \
                    else                                                         \
                    {                                                            \
                        protect_set_error_code(h.under_prot_code, h.UnderLabel); /* 设置欠限保护错误 */ \
                        h.ActFlag = PROT_UNDER;      /* 切换到欠限保护状态 */     \
                    }                                                            \
                    h.ActValue = Value;              /* 记录触发保护时的测量值 */ \
                    h.ActCount = 0;                  /* 清零保护动作计数器 */     \
                }                                                                \
            }                                                                    \
            else                                                                 \
            {                                                                    \
                h.ActCount = 0;                      /* 参数正常，清零计数器 */   \
            }                                                                    \
        }                                                                        \
        else if (h.ActFlag == PROT_DISABLE)         /* 保护功能被禁用 */         \
        {                                                                        \
            h.ActValue = Value;                      /* 仅更新测量值，不保护 */   \
        }                                                                        \
        else                                         /* 当前处于保护状态 */       \
        {                                                                        \
            if (Value < h.ActUpperThreshold && Value >= h.ActLowerThreshold)     /* 参数回到安全范围 */ \
            {                                                                    \
                h.ExtCount++;                        /* 保护退出延时计数递增 */   \
                if (h.ExtCount >= h.ExtDelay)        /* 达到保护退出延时时间 */   \
                {                                                                \
                    h.ExtCount = 0;                  /* 清零保护退出计数器 */     \
                    h.ActFlag = PROT_NORMAL;         /* 恢复到正常运行状态 */     \
                                                                                 \
                    protect_reset_error_code(h.over_prot_code);  /* 清除过限保护错误 */ \
                    protect_reset_error_code(h.under_prot_code); /* 清除欠限保护错误 */ \
                }                                                                \
            }                                                                    \
            else                                                                 \
            {                                                                    \
                h.ActValue = Value;                  /* 更新当前测量值 */         \
                h.ExtCount = 0;                      /* 参数仍超限，清零退出计数 */ \
            }                                                                    \
        }                                                                        \
    }

#endif /* SOURCE_PROTECT_H_ */  // 头文件保护宏结束
