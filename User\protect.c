/*
 * protect.c - 太阳能逆变器保护系统模块
 *
 * 功能描述:
 * 本模块实现太阳能逆变器的多级保护功能
 * 包括过流、过压、欠压、频率异常等各种故障检测和处理
 *
 * 保护策略:
 * 1. 快速保护 - 微秒级响应，立即关闭系统
 * 2. 延时保护 - 毫秒级响应，避免误触发
 * 3. 分级处理 - 根据故障严重程度采取不同措施
 *
 * 创建时间: 2024年2月19日
 * 作者: adam
 */

#include "protect.h"
#include "solar.h"

/**
 * @brief 设置保护错误代码函数
 * @param error_code 保护错误代码枚举值
 * @param text 错误描述文本字符串
 *
 * 功能: 设置系统保护状态并执行相应的保护动作
 * 调用时机: 检测到故障时立即调用
 *
 * 保护分级策略:
 * 1. 严重故障(PROT_WORD_0): 立即关闭系统，无论当前状态
 * 2. 一般故障(PROT_WORD_1): 仅在并网状态下关闭系统
 *
 * 保护动作:
 * - 设置保护标志位
 * - 根据故障等级决定是否关闭系统
 * - 发送故障信息到上位机
 */
void protect_set_error_code(enum protect_code_t error_code, const char *text)
{
    h.hProtect.protect_word[error_code / 32] |= 1 << (error_code % 32);

    if (error_code < PROT_WORD_1) // ??????????????, ??????????????, ???л????????; //??????????, ???????????????, ???л????????
    {
        if ((h.state > HSTATE_START))
        {
            solar_off();
        }
    }
    else
    {
        if (SOLAR_GET_INTERCONNECTION())
        {
            solar_off();
        }
    }

    solar_send_msg(text);
//    if (h.hProtect.protect_flag == 0)//??????
//    {
//        h.hProtect.protect_flag = 1;
//        h.hProtect.protect_code = error_code;
//        h.hProtect.text_label = text;
//    }
}

/**
 * @brief 清除保护错误代码函数
 * @param error_code 要清除的保护错误代码枚举值
 *
 * 功能: 清除指定的保护标志位
 * 调用时机: 故障消除后或系统复位时调用
 *
 * 实现原理:
 * 使用位操作清除对应的保护标志位
 * 支持多故障同时存在和独立清除
 */
void protect_reset_error_code(enum protect_code_t error_code)
{
    // 清除指定的保护标志位 (使用位操作清除单个故障标志)
    h.hProtect.protect_word[error_code / 32] &= ~(1 << (error_code % 32));
}

/**
 * @brief 保护成员初始化函数
 * @param hProt 保护成员句柄指针
 * @param ActUpperThreshold 动作上限阈值
 * @param ActLowerThreshold 动作下限阈值
 * @param ActDelay 确认保护时间 (ms)
 * @param ExtDelay 退出保护时间 (ms)
 * @param Upper_error_cord 上限错误代码
 * @param Lower_error_cord 下限错误代码
 * @param OverLabel 过限保护标签
 * @param UnderLabel 欠限保护标签
 *
 * 功能: 初始化单个保护成员的所有参数
 * 调用时机: 系统初始化时为每个保护项调用
 *
 * 保护逻辑:
 * 1. 设置上下限阈值和延时时间
 * 2. 配置保护错误代码和标签
 * 3. 初始化计数器和状态标志
 *
 * 延时保护原理:
 * - ActDelay: 检测到故障后的确认时间，避免误触发
 * - ExtDelay: 故障消除后的退出时间，确保系统稳定
 */
void Protect_Member_Init(Protect_Member_t *hProt,
                         float ActUpperThreshold, float ActLowerThreshold,
                         uint32_t ActDelay, uint32_t ExtDelay,
                         enum protect_code_t Upper_error_cord, enum protect_code_t Lower_error_cord,
                         char *OverLabel,char *UnderLabel)
{
    hProt->ActFlag = PROT_NORMAL;                 // 保护状态标志 (初始为正常状态)
    hProt->ActValue = 0;                          // 当前检测值 (初始为0)
    hProt->ActUpperThreshold = ActUpperThreshold; // 动作上限阈值
    hProt->ActLowerThreshold = ActLowerThreshold; // 动作下限阈值
    hProt->ActDelay = ActDelay;                   // 确认保护时间 (毫秒)
    hProt->ExtDelay = ExtDelay;                   // 退出保护时间 (毫秒)
    hProt->ActCount = 0;                          // 确认时间计数器 (初始为0)
    hProt->ExtCount = 0;                          // 退出时间计数器 (初始为0)

    // 配置保护错误代码和标签
    hProt->over_prot_code = Upper_error_cord;     // 过限保护错误代码
    hProt->under_prot_code = Lower_error_cord;    // 欠限保护错误代码
    hProt->OverLabel = OverLabel;                 // 过限保护文本标签
    hProt->UnderLabel = UnderLabel;               // 欠限保护文本标签
}

/**
 * @brief 保护成员检查函数
 * @param hprot 保护成员句柄指针
 * @param Val 当前检测值
 *
 * 功能: 执行单个保护成员的保护检查逻辑
 * 调用时机: 在中断服务程序中周期性调用
 *
 * 检查流程:
 * 1. 比较当前值与上下限阈值
 * 2. 执行延时确认逻辑
 * 3. 触发相应的保护动作
 *
 * 实现方式:
 * 通过宏定义实现，提高执行效率
 * 支持过限和欠限双向保护
 */
void Protect_Member_Check(Protect_Member_t *hprot, float Val)
{
    // 执行保护成员检查宏 (高效的内联实现)
    PROTECT_MEMBER_CHECK_MACRO((*hprot), (Val));
}
